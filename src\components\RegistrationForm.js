'use client';

import { useState } from 'react';
import { submitProject } from '../lib/supabase';

const RegistrationForm = ({ isOpen, onClose, isExpired = false }) => {
  const [formData, setFormData] = useState({
    teamName: '',
    urls: [],
    files: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addUrl = () => {
    setFormData(prev => ({
      ...prev,
      urls: [...prev.urls, { url: '', description: '' }]
    }));
  };

  const updateUrl = (index, field, value) => {
    const newUrls = [...formData.urls];
    newUrls[index][field] = value;
    setFormData(prev => ({
      ...prev,
      urls: newUrls
    }));
  };

  const removeUrl = (index) => {
    setFormData(prev => ({
      ...prev,
      urls: prev.urls.filter((_, i) => i !== index)
    }));
  };

  const addFile = () => {
    setFormData(prev => ({
      ...prev,
      files: [...prev.files, { file: null, description: '', uploading: false, uploadProgress: 0, error: null }]
    }));
  };

  const updateFile = (index, field, value) => {
    const newFiles = [...formData.files];
    newFiles[index][field] = value;
    setFormData(prev => ({
      ...prev,
      files: newFiles
    }));
  };

  const removeFile = (index) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index)
    }));
  };

  // File upload handlers
  const handleFileSelect = (index, file) => {
    if (file) {
      updateFile(index, 'file', file);
      // Auto-generate description from filename if empty
      if (!formData.files[index].description) {
        updateFile(index, 'description', file.name);
      }
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e, index) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(index, files[0]);
    }
  };

  // Real file upload to our API with progress tracking
  const uploadFile = async (file, onProgress = null) => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file);

      const xhr = new XMLHttpRequest();

      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const percentComplete = (event.loaded / event.total) * 100;
          onProgress(percentComplete);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const result = JSON.parse(xhr.responseText);
            resolve(result);
          } catch (error) {
            reject(new Error('Invalid response format'));
          }
        } else {
          try {
            const error = JSON.parse(xhr.responseText);
            reject(new Error(error.error || 'Upload failed'));
          } catch (error) {
            reject(new Error(`Upload failed with status ${xhr.status}`));
          }
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Network error during upload'));
      });

      xhr.open('POST', '/api/upload');
      xhr.send(formData);
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Upload files first and get their URLs
      const uploadedFiles = [];

      for (let i = 0; i < formData.files.length; i++) {
        const fileData = formData.files[i];
        if (fileData.file && fileData.description.trim()) {
          // Mark file as uploading
          updateFile(i, 'uploading', true);
          updateFile(i, 'uploadProgress', 0);
          updateFile(i, 'error', null);

          try {
            // Upload file with progress tracking
            const uploadResult = await uploadFile(fileData.file, (progress) => {
              updateFile(i, 'uploadProgress', progress);
            });

            uploadedFiles.push({
              filename: uploadResult.filename,
              description: fileData.description,
              url: uploadResult.url,
              size: uploadResult.size,
              type: uploadResult.type,
              storedName: uploadResult.storedName
            });
          } catch (uploadError) {
            console.error('Error uploading file:', uploadError);
            updateFile(i, 'error', uploadError.message);
            throw new Error(`Failed to upload ${fileData.file.name}: ${uploadError.message}`);
          } finally {
            updateFile(i, 'uploading', false);
            updateFile(i, 'uploadProgress', 0);
          }
        }
      }

      // Prepare data for submission with the new simplified structure
      const submissionData = {
        team_name: formData.teamName,
        urls: formData.urls.filter(url => url.url.trim() !== ''), // Only include non-empty URLs
        files: uploadedFiles // Use uploaded files with URLs
      };

      const { data, error } = await submitProject(submissionData);

      if (error) {
        throw error;
      }

      console.log('Project submitted successfully:', data);
      alert('Project submitted successfully!');
      onClose();

      // Reset form
      setFormData({
        teamName: '',
        urls: [],
        files: []
      });
    } catch (error) {
      console.error('Error submitting project:', error);
      alert(`Error submitting project: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-slate-900/95 backdrop-blur-md border border-white/20 rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">

        {isExpired && (
          <div className="bg-red-600/20 border border-red-500/50 rounded-lg p-4 mb-6 text-center">
            <h3 className="text-red-200 text-lg font-bold mb-2">Submission Period Ended</h3>
            <p className="text-red-300">The deadline has passed and new submissions are no longer being accepted.</p>
          </div>
        )}
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">Project Submission</h2>
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white text-2xl font-bold"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Team Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">Team Information</h3>

            <div>
              <label className="block text-white/80 text-sm font-medium mb-2">Team Name *</label>
              <input
                type="text"
                required
                value={formData.teamName}
                onChange={(e) => handleInputChange('teamName', e.target.value)}
                className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your team name"
              />
            </div>
          </div>

          {/* Project URLs */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">Project Links</h3>
            <p className="text-white/60 text-sm">Add links to your project repositories, demos, presentations, etc.</p>

            {formData.urls.map((urlField, index) => (
              <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-3 items-end p-4 bg-white/5 rounded-lg border border-white/10">
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">Link Description *</label>
                  <input
                    type="text"
                    required
                    value={urlField.description}
                    onChange={(e) => updateUrl(index, 'description', e.target.value)}
                    className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., GitHub Repository, Live Demo, Presentation"
                  />
                </div>
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">URL *</label>
                  <input
                    type="url"
                    required
                    value={urlField.url}
                    onChange={(e) => updateUrl(index, 'url', e.target.value)}
                    className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="https://..."
                  />
                </div>
                <button
                  type="button"
                  onClick={() => removeUrl(index)}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg transition-colors"
                >
                  Remove
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={addUrl}
              className="bg-blue-600/50 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors border border-blue-400/30"
            >
              + Add Project Link
            </button>
          </div>

          {/* File Uploads */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">Project Files</h3>
            <div className="space-y-2">
              <p className="text-white/60 text-sm">Upload your project files directly or drag and drop them below.</p>
              <div className="bg-yellow-600/20 border border-yellow-500/50 rounded-lg px-4 py-3">
                <p className="text-yellow-200 text-sm">
                  <strong>Note:</strong> If your file is more than 50MB, put it in Google Drive and send the link in the "Project Links" section above.
                </p>
              </div>
            </div>

            {formData.files.map((fileField, index) => (
              <div key={index} className="p-4 bg-white/5 rounded-lg border border-white/10">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-white/80 text-sm font-medium mb-2">File Description *</label>
                    <input
                      type="text"
                      required
                      value={fileField.description}
                      onChange={(e) => updateFile(index, 'description', e.target.value)}
                      className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Project Documentation, Presentation Slides"
                    />
                  </div>
                  <div className="flex items-end">
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg transition-colors w-full md:w-auto"
                    >
                      Remove
                    </button>
                  </div>
                </div>

                {/* File Upload Area */}
                <div
                  className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                    fileField.file
                      ? 'border-green-400 bg-green-400/10'
                      : 'border-white/30 hover:border-white/50 bg-white/5'
                  }`}
                  onDragOver={handleDragOver}
                  onDragEnter={handleDragEnter}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, index)}
                >
                  {fileField.uploading ? (
                    <div className="text-white/80">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                      <p>Uploading... {Math.round(fileField.uploadProgress || 0)}%</p>
                      {fileField.uploadProgress > 0 && (
                        <div className="w-full bg-white/20 rounded-full h-2 mt-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${fileField.uploadProgress}%` }}
                          ></div>
                        </div>
                      )}
                    </div>
                  ) : fileField.error ? (
                    <div className="text-red-400">
                      <div className="text-4xl mb-2">❌</div>
                      <p className="font-medium">Upload Failed</p>
                      <p className="text-sm text-red-300 mb-2">{fileField.error}</p>
                      <button
                        type="button"
                        onClick={() => {
                          updateFile(index, 'error', null);
                          updateFile(index, 'file', null);
                        }}
                        className="text-blue-400 hover:text-blue-300 text-sm underline"
                      >
                        Try again
                      </button>
                    </div>
                  ) : fileField.file ? (
                    <div className="text-white/80">
                      <div className="text-green-400 mb-2">✓</div>
                      <p className="font-medium">{fileField.file.name}</p>
                      <p className="text-sm text-white/60">
                        {(fileField.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      <button
                        type="button"
                        onClick={() => updateFile(index, 'file', null)}
                        className="mt-2 text-red-400 hover:text-red-300 text-sm underline"
                      >
                        Remove file
                      </button>
                    </div>
                  ) : (
                    <div className="text-white/60">
                      <div className="text-4xl mb-2">📁</div>
                      <p className="mb-2">Drag and drop your file here, or</p>
                      <label className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg cursor-pointer transition-colors">
                        Choose File
                        <input
                          type="file"
                          className="hidden"
                          onChange={(e) => handleFileSelect(index, e.target.files[0])}
                          accept=".pdf,.doc,.docx,.ppt,.pptx,.zip,.rar,.txt,.md,.jpg,.jpeg,.png,.gif,.mp4,.mov,.avi"
                        />
                      </label>
                      <p className="text-xs mt-2 text-white/40">
                        Supported: PDF, DOC, PPT, ZIP, Images, Videos, etc. (Max 50MB)
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ))}

            <button
              type="button"
              onClick={addFile}
              className="bg-purple-600/50 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors border border-purple-400/30"
            >
              + Add File Upload
            </button>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 border border-white/30 text-white rounded-lg hover:bg-white/10 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || isExpired || !formData.teamName.trim()}
              className="px-8 py-3 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isExpired ? 'Submissions Closed' : isSubmitting ? 'Submitting...' : 'Submit Project'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RegistrationForm;
