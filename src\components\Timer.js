'use client';

import { useState, useEffect, useCallback } from 'react';
import { getTimerConfig } from '../lib/supabase';

const Timer = ({ initialMinutes = 60, onRegister, onExpired }) => {
  const [timeLeft, setTimeLeft] = useState(initialMinutes * 60); // Convert to seconds
  const [isRunning, setIsRunning] = useState(true); // Auto-start the timer
  const [isExpired, setIsExpired] = useState(false);
  const [timerConfig, setTimerConfig] = useState(null);
  const [supabaseError, setSupabaseError] = useState(null);
  const [isUsingFallback, setIsUsingFallback] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Load timer configuration from Supabase with retry logic and fallback
  useEffect(() => {
    const loadTimerConfig = async (attempt = 1) => {
      try {
        console.log(`Loading timer config (attempt ${attempt})`);
        const { data, error } = await getTimerConfig();

        if (error) {
          console.warn('Supabase error:', error);
          setSupabaseError(error);

          // Handle specific error cases
          if (error.code === 'ENV_MISSING' || error.code === 'TABLE_MISSING') {
            // Critical errors - use fallback immediately
            console.log('Using fallback timer due to configuration issue');
            setIsUsingFallback(true);
            return;
          }

          // For other errors, retry up to 3 times
          if (attempt < 3) {
            console.log(`Retrying in ${attempt * 2} seconds...`);
            setTimeout(() => loadTimerConfig(attempt + 1), attempt * 2000);
            setRetryCount(attempt);
            return;
          } else {
            // Max retries reached, use fallback
            console.log('Max retries reached, using fallback timer');
            setIsUsingFallback(true);
            return;
          }
        }

        if (data) {
          console.log('Successfully loaded timer config from Supabase:', data);
          setTimerConfig(data);
          setSupabaseError(null);
          setIsUsingFallback(false);

          const endTime = new Date(data.end_time);
          const now = new Date();
          const remainingSeconds = Math.max(0, Math.floor((endTime - now) / 1000));

          setTimeLeft(remainingSeconds);
          if (remainingSeconds === 0) {
            setIsExpired(true);
            setIsRunning(false);
            if (onExpired) onExpired();
          }
        } else {
          // No timer config found, use fallback
          console.log('No timer configuration found, using fallback');
          setIsUsingFallback(true);
        }
      } catch (error) {
        console.error('Unexpected error loading timer config:', error);
        setSupabaseError(error);

        // For unexpected errors, also try retry logic
        if (attempt < 3) {
          console.log(`Retrying after unexpected error in ${attempt * 2} seconds...`);
          setTimeout(() => loadTimerConfig(attempt + 1), attempt * 2000);
          setRetryCount(attempt);
        } else {
          console.log('Max retries reached after unexpected error, using fallback timer');
          setIsUsingFallback(true);
        }
      }
    };

    loadTimerConfig();
  }, [onExpired]);

  useEffect(() => {
    let interval = null;

    if (isRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(time => {
          if (time <= 1) {
            setIsRunning(false);
            setIsExpired(true);
            if (onExpired) onExpired();
            return 0;
          }
          return time - 1;
        });
      }, 1000);
    } else if (!isRunning) {
      clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, [isRunning, timeLeft]);

  const formatTime = useCallback((seconds) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    // Smart time display logic based on duration
    if (days >= 1) {
      // 1+ days: show "X days, Y hours" format
      if (hours === 0) {
        return days === 1 ? '1 day' : `${days} days`;
      }
      return days === 1
        ? `1 day, ${hours} ${hours === 1 ? 'hour' : 'hours'}`
        : `${days} days, ${hours} ${hours === 1 ? 'hour' : 'hours'}`;
    } else if (hours >= 1) {
      // Less than 24 hours: show "X hours, Y minutes" format
      if (minutes === 0) {
        return hours === 1 ? '1 hour' : `${hours} hours`;
      }
      return hours === 1
        ? `1 hour, ${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`
        : `${hours} hours, ${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
    } else if (minutes >= 1) {
      // Less than 1 hour: show "X minutes, Y seconds" format
      if (secs === 0) {
        return minutes === 1 ? '1 minute' : `${minutes} minutes`;
      }
      return minutes === 1
        ? `1 minute, ${secs} ${secs === 1 ? 'second' : 'seconds'}`
        : `${minutes} minutes, ${secs} ${secs === 1 ? 'second' : 'seconds'}`;
    } else {
      // Less than 1 minute: show "X seconds" format
      return secs === 1 ? '1 second' : `${secs} seconds`;
    }
  }, []);

  const getTimeColor = () => {
    if (timeLeft <= 300) return 'animate-flash-red'; // Last 5 minutes - flashing red
    if (timeLeft <= 600) return 'text-yellow-400'; // Last 10 minutes
    return 'text-white';
  };

  const getTimerContainerClass = () => {
    if (timeLeft <= 300) return 'animate-pulse-glow'; // Add glow effect for last 5 minutes
    return '';
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] px-4">
      {/* Status Indicator */}
      {(isUsingFallback || supabaseError) && (
        <div className="mb-4 text-center">
          {retryCount > 0 && retryCount < 3 && (
            <div className="bg-yellow-600/20 border border-yellow-500/50 rounded-lg px-4 py-2 mb-2">
              <p className="text-yellow-200 text-sm">
                Connecting to server... (attempt {retryCount}/3)
              </p>
            </div>
          )}
          {isUsingFallback && (
            <div className="bg-blue-600/20 border border-blue-500/50 rounded-lg px-4 py-2">
              <p className="text-blue-200 text-sm">
                Using local timer ({initialMinutes} minutes)
              </p>
            </div>
          )}
        </div>
      )}

      {/* Timer Display - Clean and Prominent */}
      <div className={`text-center ${getTimerContainerClass()}`}>
        <div className={`timer-mega-display text-ultra-sm sm:text-ultra-md md:text-ultra-lg lg:text-ultra-xl xl:text-ultra-2xl 2xl:text-ultra-3xl ${getTimeColor()} drop-shadow-2xl`}>
          {formatTime(timeLeft)}
        </div>
      </div>

      {/* Submissions Button or Expired Message */}
      <div className="flex justify-center mt-12">
        {isExpired ? (
          <div className="text-center">
            <div className="bg-red-600/20 border border-red-500/50 rounded-2xl px-12 py-6 mb-4">
              <h3 className="text-red-200 text-2xl font-bold mb-2">TIME'S UP!</h3>
              <p className="text-red-300">The submission period has ended.</p>
              <p className="text-red-300/80 text-sm mt-2">No new submissions are being accepted.</p>
            </div>
          </div>
        ) : (
          <button
            onClick={onRegister}
            className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white px-12 py-4 rounded-full font-bold text-xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105"
          >
            SUBMISSIONS
          </button>
        )}
      </div>
    </div>
  );
};

export default Timer;
