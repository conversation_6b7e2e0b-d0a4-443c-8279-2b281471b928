'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';

const BackgroundAnimation = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Background assets (excluding the main logos)
  const backgroundAssets = [
    { src: '/bleu_ball.png', size: 60 },
    { src: '/red_ball.png', size: 50 },
    { src: '/plus_shape.png', size: 40 },
    { src: '/cillender.png', size: 70 },
    { src: '/bleu_ball.png', size: 60 },
    { src: '/red_ball.png', size: 50 },
    { src: '/plus_shape.png', size: 40 },
    { src: '/cillender.png', size: 70 },
    { src: '/bleu_ball.png', size: 60 },
    { src: '/red_ball.png', size: 50 },
    { src: '/plus_shape.png', size: 40 },
    { src: '/cillender.png', size: 70 },
    
  ];

  // Generate random positions for floating assets
  const generateRandomAssets = () => {
    return backgroundAssets.map((asset, index) => ({
      ...asset,
      id: index,
      x: Math.random() * 80 + 10, // 10% to 90% of screen width
      y: Math.random() * 80 + 10, // 10% to 90% of screen height
      rotation: Math.random() * 360,
      scale: 0.5 + Math.random() * 0.5, // 0.5 to 1.0 scale
      animationDelay: Math.random() * 5, // 0 to 5 seconds delay
      animationDuration: 10 + Math.random() * 20, // 10 to 30 seconds
    }));
  };

  const [randomAssets] = useState(() => generateRandomAssets());

  if (!mounted) return null;

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">


      {/* Randomly distributed floating assets */}
      {randomAssets.map((asset) => (
        <div
          key={asset.id}
          className="absolute animate-float opacity-10 sm:opacity-20 hover:opacity-40 transition-opacity duration-500 hidden sm:block background-element"
          style={{
            left: `${asset.x}%`,
            top: `${asset.y}%`,
            transform: `rotate(${asset.rotation}deg) scale(${asset.scale})`,
            animationDelay: `${asset.animationDelay}s`,
            animationDuration: `${asset.animationDuration}s`,
          }}
        >
          <div className="relative" style={{ width: asset.size * 0.8, height: asset.size * 0.8 }}>
            <Image
              src={asset.src}
              alt=""
              fill
              className="object-contain drop-shadow-lg"
            />
          </div>
        </div>
      ))}



      {/* Gradient overlays for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20"></div>
      <div className="absolute inset-0 bg-gradient-to-tl from-cyan-900/10 via-transparent to-indigo-900/10"></div>
      
      {/* Animated particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full opacity-30 animate-twinkle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          ></div>
        ))}
      </div>
    </div>
  );
};

export default BackgroundAnimation;
