// Test script to verify the simplified timer format
// Run with: node test-timer-format.js

const formatTime = (seconds) => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  // Simple time format: DD:HH:MM:SS or HH:MM:SS or MM:SS
  if (days > 0) {
    return `${days.toString().padStart(2, '0')}:${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
};

// Test cases
const testCases = [
  { seconds: 30, expected: '00:30' },
  { seconds: 90, expected: '01:30' },
  { seconds: 3600, expected: '01:00:00' },
  { seconds: 3661, expected: '01:01:01' },
  { seconds: 86400, expected: '01:00:00:00' },
  { seconds: 90061, expected: '01:01:01:01' },
  { seconds: 172861, expected: '02:01:01:01' }
];

console.log('Testing simplified timer format...\n');

let passed = 0;
let failed = 0;

testCases.forEach(({ seconds, expected }) => {
  const result = formatTime(seconds);
  const success = result === expected;
  
  console.log(`${success ? '✅' : '❌'} ${seconds}s -> "${result}" ${success ? '' : `(expected: "${expected}")`}`);
  
  if (success) {
    passed++;
  } else {
    failed++;
  }
});

console.log(`\nResults: ${passed} passed, ${failed} failed`);

if (failed === 0) {
  console.log('🎉 All tests passed! Simplified timer format is working correctly.');
} else {
  console.log('❌ Some tests failed. Please check the formatting logic.');
}
