import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Main client for anonymous operations (submissions)
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false, // Disable session persistence to avoid auth errors for anonymous users
    autoRefreshToken: false, // Disable auto refresh for anonymous usage
  }
})

// Admin client for authenticated operations
export const supabaseAdmin = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true, // Enable session persistence for admin users
    autoRefreshToken: true, // Enable auto refresh for admin sessions
  }
})

// Database schema and helper functions
export const createTables = async () => {
  // This would typically be done via Supabase dashboard or migrations
  // Including here for reference of the expected schema
  
  const submissions_table = `
    CREATE TABLE IF NOT EXISTS submissions (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      team_name VA<PERSON><PERSON>R NOT NULL,
      team_leader VARCHAR NOT NULL,
      email VARCHAR NOT NULL,
      phone VARCHAR,
      university VARCHAR,
      members JSONB DEFAULT '[]',
      experience VARCHAR,
      project_description TEXT NOT NULL,
      urls JSONB DEFAULT '{}',
      files JSONB DEFAULT '[]',
      custom_fields JSONB DEFAULT '{}',
      submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;

  const timer_config_table = `
    CREATE TABLE IF NOT EXISTS timer_config (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      end_time TIMESTAMP WITH TIME ZONE NOT NULL,
      is_active BOOLEAN DEFAULT true,
      created_by UUID REFERENCES auth.users(id),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;

  const admin_users_table = `
    CREATE TABLE IF NOT EXISTS admin_users (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id UUID REFERENCES auth.users(id) UNIQUE,
      email VARCHAR NOT NULL,
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;

  // Row Level Security policies would be set up in Supabase dashboard
  console.log('Database schema defined:', {
    submissions_table,
    timer_config_table,
    admin_users_table
  });
};

// Submission operations
export const submitProject = async (formData) => {
  try {
    // Validate environment variables first
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return { data: null, error: { message: 'Supabase not configured', code: 'ENV_MISSING' } };
    }

    // Create a new client instance for anonymous submissions to avoid auth session issues
    const anonClient = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      }
    });

    const { data, error } = await anonClient
      .from('submissions')
      .insert([formData])
      .select();

    if (error) {
      console.error('Supabase submission error:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      throw error;
    }
    return { data, error: null };
  } catch (error) {
    console.error('Error submitting project:', {
      message: error.message,
      code: error.code,
      details: error.details,
      hint: error.hint
    });
    return { data: null, error: {
      message: error.message || 'Failed to submit project',
      code: error.code || 'UNKNOWN_ERROR',
      originalError: error
    } };
  }
};

export const getAllSubmissions = async () => {
  try {
    const { data, error } = await supabaseAdmin
      .from('submissions')
      .select('*')
      .order('submitted_at', { ascending: false });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching submissions:', error);
    return { data: null, error };
  }
};

// Timer operations
export const getTimerConfig = async () => {
  try {
    // Validate environment variables first
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not configured');
      return { data: null, error: { message: 'Supabase not configured', code: 'ENV_MISSING' } };
    }

    const { data, error } = await supabase
      .from('timer_config')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      // Handle specific error cases
      if (error.code === 'PGRST116') {
        // No rows returned - this is expected when no timer is configured
        console.log('No active timer configuration found');
        return { data: null, error: null };
      } else if (error.code === '42P01') {
        // Table doesn't exist
        console.warn('Timer config table does not exist');
        return { data: null, error: { message: 'Timer table not found', code: 'TABLE_MISSING' } };
      } else {
        // Other database errors
        console.error('Database error fetching timer config:', error);
        throw error;
      }
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error fetching timer config:', {
      message: error.message,
      code: error.code,
      details: error.details,
      hint: error.hint
    });
    return { data: null, error: {
      message: error.message || 'Failed to fetch timer configuration',
      code: error.code || 'UNKNOWN_ERROR',
      originalError: error
    } };
  }
};

export const updateTimerConfig = async (endTime) => {
  try {
    // Deactivate existing timers
    await supabaseAdmin
      .from('timer_config')
      .update({ is_active: false })
      .eq('is_active', true);

    // Create new timer config
    const { data, error } = await supabaseAdmin
      .from('timer_config')
      .insert([{
        end_time: endTime,
        is_active: true
      }])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating timer config:', error);
    return { data: null, error };
  }
};

// Auth operations
export const signInWithEmail = async (email, password) => {
  try {
    const { data, error } = await supabaseAdmin.auth.signInWithPassword({
      email,
      password
    });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error signing in:', error);
    return { data: null, error };
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabaseAdmin.auth.signOut();
    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error signing out:', error);
    return { error };
  }
};

export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabaseAdmin.auth.getUser();
    if (error) {
      console.log('No authenticated user session');
      return { user: null, error };
    }
    return { user, error: null };
  } catch (error) {
    console.error('Error getting current user:', error);
    return { user: null, error };
  }
};

export const checkAdminStatus = async (userId, userEmail = null) => {
  try {
    // First try to check by user_id
    let { data, error } = await supabaseAdmin
      .from('admin_users')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    // If no result and we have an email, try checking by email
    if ((!data || error?.code === 'PGRST116') && userEmail) {
      const { data: emailData, error: emailError } = await supabaseAdmin
        .from('admin_users')
        .select('*')
        .eq('email', userEmail)
        .eq('is_active', true)
        .single();

      data = emailData;
      error = emailError;
    }

    if (error && error.code !== 'PGRST116') throw error;
    return { isAdmin: !!data, error: null };
  } catch (error) {
    console.error('Error checking admin status:', error);
    return { isAdmin: false, error };
  }
};
