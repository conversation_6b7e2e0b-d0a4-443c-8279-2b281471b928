import Image from 'next/image';

const LogoLayout = () => {
  return (
    <div className="flex flex-col items-center space-y-4 sm:space-y-6 md:space-y-8 z-10 relative px-4">
      {/* Top: Hackathon Logo */}
      <div className="flex justify-center">
        <Image
          src="/ncshack_logo.png"
          alt="Hackathon Logo"
          width={200}
          height={80}
          className="object-contain drop-shadow-lg sm:w-[250px] sm:h-[100px] md:w-[300px] md:h-[120px]"
          priority
        />
      </div>

      {/* Middle: University + X + Club Layout */}
      <div className="flex items-center justify-center space-x-3 sm:space-x-4 md:space-x-6 lg:space-x-8">
        {/* University Logo */}
        <Image
          src="/logo_university.png"
          alt="University Logo"
          width={100}
          height={100}
          className="object-contain drop-shadow-md sm:w-[110px] sm:h-[110px] md:w-[120px] md:h-[120px]"
        />

        {/* X Symbol */}
        <span className="text-white font-bold text-2xl sm:text-3xl md:text-4xl drop-shadow-lg">×</span>

        {/* Club Logo */}
        <Image
          src="/logo_club.png"
          alt="Club Logo"
          width={100}
          height={100}
          className="object-contain drop-shadow-md sm:w-[110px] sm:h-[110px] md:w-[120px] md:h-[120px]"
        />
      </div>
    </div>
  );
};

export default LogoLayout;
