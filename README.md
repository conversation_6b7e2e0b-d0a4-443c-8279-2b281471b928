# Hackathon Timer Web Application

A beautiful, modern countdown timer designed specifically for hackathon events. Built with Next.js and Tailwind CSS, featuring clean design inspired by professional hackathon posters, animated backgrounds, and integrated registration functionality.

## ✨ Features

### ⏱️ Countdown Timer
- **Auto-starting countdown timer** with large, bold display
- **Color-coded time warnings** (red for last 5 minutes, yellow for last 10 minutes)
- **Clean typography** with "TIME REMAINING" label
- **Real-time updates** with smooth performance

### 🎨 Modern Design
- **Clean, minimal aesthetic** inspired by professional hackathon posters
- **Blue gradient background** with subtle grid pattern
- **Logo display**: Hackathon logo at top, University × Club logos below
- **No visual containers** - clean, uncluttered appearance

### 🌟 Background Animation
- **Mesh assets** with smooth floating animations in corners
- **Decorative elements**: Colored circles and plus signs
- **Floating background assets** with optimized performance
- **Subtle grid pattern** matching reference design

### 📝 Registration System
- **Single REGISTER button** prominently displayed below timer
- **Modal registration form** with comprehensive fields:
  - Team information (name, leader, university)
  - Contact details (email, phone)
  - Team members (up to 4 total)
  - Experience level and project ideas
- **Responsive form design** with validation

### 📱 Responsive Design
- **Mobile-first approach** optimized for all screen sizes
- **Touch-friendly interface** for mobile devices
- **Performance optimizations** for smooth animations

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd timerncs
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📁 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles, animations, and grid pattern
│   ├── layout.js           # Root layout component
│   └── page.js             # Main page with timer and registration
└── components/
    ├── Timer.js            # Countdown timer component
    ├── LogoLayout.js       # Clean logo arrangement
    ├── BackgroundAnimation.js # Animated background system
    └── RegistrationForm.js # Modal registration form

public/
├── ncshack_logo.png        # Main hackathon logo
├── logo_university.png     # University logo
├── logo_club.png          # Club logo
├── mesh.png               # Background mesh asset
└── [other assets]         # Additional floating elements
```

## 🎨 Design Features

### Visual Elements
- **Grid background pattern** matching reference design
- **Blue to purple gradient** background
- **Floating decorative circles** with pulse animations
- **Plus sign decorations** positioned strategically
- **Clean typography** with bold, modern fonts

### Animation System
- **Smooth mesh floating** in corners with enhanced movement
- **Decorative element animations** with staggered timing
- **Performance optimized** with CSS transforms and will-change
- **Responsive animations** that adapt to screen size

### Registration Form
- **Modal overlay** with backdrop blur
- **Comprehensive form fields** for team registration
- **Validation and submission** handling
- **Responsive design** for all devices

## 🛠️ Customization

### Changing Timer Duration
Edit the initial timer value in `src/app/page.js`:
```javascript
<Timer initialMinutes={60} onRegister={handleOpenRegistration} />
```

### Updating Logos
Replace the logo files in the `public/` directory:
- `ncshack_logo.png` - Main hackathon logo
- `logo_university.png` - University logo
- `logo_club.png` - Club logo

### Modifying Colors
Update the color scheme in `src/app/globals.css` and component files. Current theme uses:
- Blue to purple gradients (`from-blue-900 via-indigo-900 to-purple-900`)
- White text with opacity variations
- Grid pattern with subtle white lines

### Registration Form Fields
Customize form fields in `src/components/RegistrationForm.js` by modifying the `formData` state and form JSX.

## 🔧 Technical Details

### Performance Optimizations
- **Hardware acceleration** with CSS transforms
- **Optimized animations** using `will-change` properties
- **Efficient re-renders** with React hooks
- **Responsive asset loading** for different screen sizes

### Browser Support
- Chrome/Edge 88+
- Firefox 85+
- Safari 14+
- Mobile browsers (iOS Safari, Chrome Mobile)

### Technologies Used
- **Next.js 15** - React framework with App Router
- **React 19** - UI library with modern hooks
- **Tailwind CSS 4** - Utility-first CSS framework
- **CSS Grid & Flexbox** - Layout systems
- **CSS Animations** - Smooth transitions and effects

## 📱 Responsive Breakpoints

- **Mobile**: < 640px (sm)
- **Tablet**: 640px - 768px (md)
- **Desktop**: 768px - 1024px (lg)
- **Large Desktop**: > 1024px (xl)

## 🎯 Usage

1. **Timer Display**: Shows countdown automatically starting when page loads
2. **Registration**: Click the REGISTER button to open the registration form
3. **Form Submission**: Fill out team details and submit registration
4. **Responsive**: Works seamlessly on desktop, tablet, and mobile devices

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on different devices
5. Submit a pull request

---

Built with ❤️ for hackathon events • Inspired by modern design principles
