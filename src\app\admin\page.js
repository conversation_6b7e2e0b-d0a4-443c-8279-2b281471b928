'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  getCurrentUser, 
  checkAdminStatus, 
  signOut, 
  getAllSubmissions, 
  getTimerConfig, 
  updateTimerConfig 
} from '../../lib/supabase';

export default function AdminDashboard() {
  const [user, setUser] = useState(null);
  const [submissions, setSubmissions] = useState([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState([]);
  const [timerConfig, setTimerConfig] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('submissions');
  const [newEndTime, setNewEndTime] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const router = useRouter();

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        // For now, skip authentication and load data directly
        // TODO: Implement proper authentication when needed
        console.log('Loading admin dashboard data...');

        // Set a mock user for display purposes
        setUser({ email: '<EMAIL>' });

        await loadSubmissions();
        await loadTimerConfig();
      } catch (error) {
        console.error('Error loading admin data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthAndLoadData();
  }, [router]);

  const loadSubmissions = async () => {
    const { data, error } = await getAllSubmissions();
    if (data) {
      setSubmissions(data);
      setFilteredSubmissions(data);
    } else if (error) {
      console.error('Error loading submissions:', error);
    }
  };

  // Filter submissions based on search term
  const handleSearch = (term) => {
    setSearchTerm(term);
    if (!term.trim()) {
      setFilteredSubmissions(submissions);
    } else {
      const filtered = submissions.filter(submission =>
        submission.team_name.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredSubmissions(filtered);
    }
  };

  const loadTimerConfig = async () => {
    const { data, error } = await getTimerConfig();
    if (data) {
      setTimerConfig(data);
      setNewEndTime(new Date(data.end_time).toISOString().slice(0, 16));
    } else if (error) {
      console.error('Error loading timer config:', error);
    }
  };

  const handleSignOut = async () => {
    // For now, just redirect to home
    router.push('/');
  };

  const handleUpdateTimer = async (e) => {
    e.preventDefault();
    try {
      const endTime = new Date(newEndTime).toISOString();
      const { data, error } = await updateTimerConfig(endTime);
      
      if (error) {
        throw error;
      }

      setTimerConfig(data);
      alert('Timer updated successfully!');
    } catch (error) {
      console.error('Error updating timer:', error);
      alert('Error updating timer. Please try again.');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  // Helper function to safely parse JSON
  const safeJsonParse = (jsonString, fallback = []) => {
    try {
      return JSON.parse(jsonString || '[]');
    } catch (error) {
      console.warn('Failed to parse JSON:', jsonString);
      return fallback;
    }
  };

  // Helper function to normalize URLs data (handle both old and new formats)
  const normalizeUrls = (urlsData) => {
    if (!urlsData) return [];

    // If it's already an array (new format), return as is
    if (Array.isArray(urlsData)) {
      return urlsData.filter(item => item.url && item.url.trim() !== '');
    }

    // If it's an object (old format), convert to new format
    if (typeof urlsData === 'object') {
      const urls = [];

      // Handle named fields
      if (urlsData.github && urlsData.github.trim()) {
        urls.push({ url: urlsData.github, description: 'GitHub Repository' });
      }
      if (urlsData.googleDrive && urlsData.googleDrive.trim()) {
        urls.push({ url: urlsData.googleDrive, description: 'Google Drive' });
      }
      if (urlsData.figma && urlsData.figma.trim()) {
        urls.push({ url: urlsData.figma, description: 'Figma Design' });
      }

      // Handle additional URLs
      if (urlsData.additional && Array.isArray(urlsData.additional)) {
        urlsData.additional.forEach(item => {
          if (item.url && item.url.trim()) {
            urls.push({
              url: item.url,
              description: item.label || 'Additional Link'
            });
          }
        });
      }

      return urls;
    }

    return [];
  };

  // Helper function to normalize Files data (handle both old and new formats)
  const normalizeFiles = (filesData) => {
    if (!filesData) return [];

    // If it's already an array with filename/description (new format), return as is
    if (Array.isArray(filesData)) {
      return filesData.filter(item => {
        // New format: {filename, description, url?, size?, type?}
        if (item.filename && item.filename.trim() !== '') return true;
        // Old format: {label, file} or just strings
        if (item.label || item.file || typeof item === 'string') return true;
        return false;
      }).map(item => {
        // Normalize to new format
        if (item.filename) return item; // Already new format
        if (typeof item === 'string') return { filename: item, description: 'File' };
        return {
          filename: item.file || item.label || 'Unknown file',
          description: item.label || 'File upload'
        };
      });
    }

    return [];
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 grid-background flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 grid-background">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-md border-b border-white/20 p-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <h1 className="text-2xl font-bold text-white">Hackathon Admin Dashboard</h1>
          <div className="flex items-center space-x-4">
            <span className="text-white/60">Welcome, {user?.email}</span>
            <button
              onClick={handleSignOut}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="max-w-7xl mx-auto p-4">
        <div className="flex space-x-4 mb-6">
          <button
            onClick={() => setActiveTab('submissions')}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              activeTab === 'submissions'
                ? 'bg-blue-600 text-white'
                : 'bg-white/10 text-white/70 hover:bg-white/20'
            }`}
          >
            Submissions ({searchTerm ? filteredSubmissions.length : submissions.length})
          </button>
          <button
            onClick={() => setActiveTab('timer')}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              activeTab === 'timer'
                ? 'bg-blue-600 text-white'
                : 'bg-white/10 text-white/70 hover:bg-white/20'
            }`}
          >
            Timer Control
          </button>
        </div>

        {/* Submissions Tab */}
        {activeTab === 'submissions' && (
          <div className="bg-slate-900/95 backdrop-blur-md border border-white/20 rounded-2xl p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-white">Project Submissions</h2>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search team names..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="bg-white/10 border border-white/30 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                  />
                  {searchTerm && (
                    <button
                      onClick={() => handleSearch('')}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white"
                    >
                      ✕
                    </button>
                  )}
                </div>
                <button
                  onClick={loadSubmissions}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Refresh
                </button>
              </div>
            </div>

            {searchTerm && (
              <div className="mb-4 text-white/70 text-sm">
                Showing {filteredSubmissions.length} of {submissions.length} submissions
                {searchTerm && ` for "${searchTerm}"`}
              </div>
            )}

            {filteredSubmissions.length === 0 ? (
              <div className="text-center text-white/60 py-8">
                {searchTerm ? `No submissions found for "${searchTerm}"` : 'No submissions yet.'}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full text-white text-sm">
                  <thead>
                    <tr className="border-b border-white/20">
                      <th className="text-left p-3 font-semibold">Team Name</th>
                      <th className="text-left p-3 font-semibold">Project Links</th>
                      <th className="text-left p-3 font-semibold">Project Files</th>
                      <th className="text-left p-3 font-semibold">Submitted</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredSubmissions.map((submission) => (
                      <tr key={submission.id} className="border-b border-white/10 hover:bg-white/5">
                        <td className="p-3 align-top">
                          <div className="font-medium text-white text-lg">
                            {submission.team_name}
                          </div>
                        </td>
                        <td className="p-3 align-top">
                          <div className="space-y-2">
                            {(() => {
                              const normalizedUrls = normalizeUrls(submission.urls);
                              return normalizedUrls.length > 0 ? (
                                normalizedUrls.map((urlItem, index) => (
                                  <div key={index} className="text-xs">
                                    <div className="text-white/80 font-medium mb-1">
                                      {urlItem.description}
                                    </div>
                                    <a
                                      href={urlItem.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-blue-400 hover:text-blue-300 underline break-all"
                                    >
                                      {urlItem.url}
                                    </a>
                                  </div>
                                ))
                              ) : (
                                <div className="text-white/40 text-xs italic">No links provided</div>
                              );
                            })()}
                          </div>
                        </td>
                        <td className="p-3 align-top">
                          <div className="space-y-2">
                            {(() => {
                              const normalizedFiles = normalizeFiles(submission.files);
                              return normalizedFiles.length > 0 ? (
                                normalizedFiles.map((fileItem, index) => (
                                  <div key={index} className="text-xs">
                                    <div className="text-white/80 font-medium mb-1">
                                      {fileItem.description}
                                    </div>
                                    <div className="text-white/60">
                                      {fileItem.url ? (
                                        <div className="flex items-center space-x-2">
                                          <a
                                            href={fileItem.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-400 hover:text-blue-300 underline"
                                            download={fileItem.filename}
                                          >
                                            {fileItem.filename}
                                          </a>
                                          <a
                                            href={fileItem.url}
                                            download={fileItem.filename}
                                            className="text-green-400 hover:text-green-300 text-xs"
                                            title="Download file"
                                          >
                                            ⬇️
                                          </a>
                                        </div>
                                      ) : (
                                        fileItem.filename
                                      )}
                                      {fileItem.size && (
                                        <span className="text-white/40 ml-2">
                                          ({(fileItem.size / 1024 / 1024).toFixed(2)} MB)
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                ))
                              ) : (
                                <div className="text-white/40 text-xs italic">No files provided</div>
                              );
                            })()}
                          </div>
                        </td>
                        <td className="p-3 align-top">
                          <div className="text-white/70 text-xs">
                            {formatDate(submission.submitted_at)}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Timer Control Tab */}
        {activeTab === 'timer' && (
          <div className="bg-slate-900/95 backdrop-blur-md border border-white/20 rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Timer Configuration</h2>
            
            {timerConfig && (
              <div className="mb-6 p-4 bg-white/10 rounded-lg">
                <h3 className="text-white font-medium mb-2">Current Timer</h3>
                <p className="text-white/70">End Time: {formatDate(timerConfig.end_time)}</p>
                <p className="text-white/70">Status: {timerConfig.is_active ? 'Active' : 'Inactive'}</p>
              </div>
            )}

            <form onSubmit={handleUpdateTimer} className="space-y-4">
              <div>
                <label className="block text-white/80 text-sm font-medium mb-2">
                  Set New End Time
                </label>
                <input
                  type="datetime-local"
                  required
                  value={newEndTime}
                  onChange={(e) => setNewEndTime(e.target.value)}
                  className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Update Timer
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
